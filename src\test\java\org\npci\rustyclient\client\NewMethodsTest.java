package org.npci.rustyclient.client;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the new methods added to RustyCluster client.
 * These tests verify the API structure and method signatures of HMSET, HEXISTS, EXISTS, SETNX,
 * LoadScript, EvalSha, and HealthCheck methods.
 *
 * Note: These tests focus on API validation and method signature verification.
 * They don't require a running RustyCluster server.
 */
public class NewMethodsTest {

    @Test
    @DisplayName("Test new method signatures exist in RustyClusterClient")
    void testSyncClientMethodSignatures() throws Exception {
        Class<RustyClusterClient> clientClass = RustyClusterClient.class;

        // Test HMSET method signatures
        Method hMSetMethod1 = clientClass.getMethod("hMSet", String.class, Map.class);
        assertNotNull(hMSetMethod1, "hMSet(String, Map) method should exist");
        assertEquals(boolean.class, hMSetMethod1.getReturnType(), "hMSet should return boolean");

        Method hMSetMethod2 = clientClass.getMethod("hMSet", String.class, Map.class, boolean.class);
        assertNotNull(hMSetMethod2, "hMSet(String, Map, boolean) method should exist");
        assertEquals(boolean.class, hMSetMethod2.getReturnType(), "hMSet with skipReplication should return boolean");

        // Test HEXISTS method signature
        Method hExistsMethod = clientClass.getMethod("hExists", String.class, String.class);
        assertNotNull(hExistsMethod, "hExists(String, String) method should exist");
        assertEquals(boolean.class, hExistsMethod.getReturnType(), "hExists should return boolean");

        // Test EXISTS method signature
        Method existsMethod = clientClass.getMethod("exists", String.class);
        assertNotNull(existsMethod, "exists(String) method should exist");
        assertEquals(boolean.class, existsMethod.getReturnType(), "exists should return boolean");

        // Test SETNX method signatures
        Method setNXMethod1 = clientClass.getMethod("setNX", String.class, String.class);
        assertNotNull(setNXMethod1, "setNX(String, String) method should exist");
        assertEquals(boolean.class, setNXMethod1.getReturnType(), "setNX should return boolean");

        Method setNXMethod2 = clientClass.getMethod("setNX", String.class, String.class, boolean.class);
        assertNotNull(setNXMethod2, "setNX(String, String, boolean) method should exist");
        assertEquals(boolean.class, setNXMethod2.getReturnType(), "setNX with skipReplication should return boolean");

        // Test LoadScript method signature
        Method loadScriptMethod = clientClass.getMethod("loadScript", String.class);
        assertNotNull(loadScriptMethod, "loadScript(String) method should exist");
        assertEquals(String.class, loadScriptMethod.getReturnType(), "loadScript should return String");

        // Test EvalSha method signatures
        Method evalShaMethod1 = clientClass.getMethod("evalSha", String.class, List.class, List.class);
        assertNotNull(evalShaMethod1, "evalSha(String, List, List) method should exist");
        assertEquals(String.class, evalShaMethod1.getReturnType(), "evalSha should return String");

        Method evalShaMethod2 = clientClass.getMethod("evalSha", String.class, List.class, List.class, boolean.class);
        assertNotNull(evalShaMethod2, "evalSha(String, List, List, boolean) method should exist");
        assertEquals(String.class, evalShaMethod2.getReturnType(), "evalSha with skipReplication should return String");

        // Test HealthCheck method signature
        Method healthCheckMethod = clientClass.getMethod("healthCheck");
        assertNotNull(healthCheckMethod, "healthCheck() method should exist");
        assertEquals(boolean.class, healthCheckMethod.getReturnType(), "healthCheck should return boolean");

        // Test Ping method signature
        Method pingMethod = clientClass.getMethod("ping");
        assertNotNull(pingMethod, "ping() method should exist");
        assertEquals(boolean.class, pingMethod.getReturnType(), "ping should return boolean");
    }

    @Test
    @DisplayName("Test async method signatures exist in main RustyClusterClient")
    void testAsyncClientMethodSignatures() throws Exception {
        Class<RustyClusterClient> clientClass = RustyClusterClient.class;

        // Test HMSET async method signatures
        Method hMSetAsyncMethod1 = clientClass.getMethod("hMSetAsync", String.class, Map.class);
        assertNotNull(hMSetAsyncMethod1, "hMSetAsync(String, Map) method should exist");
        assertEquals(CompletableFuture.class, hMSetAsyncMethod1.getReturnType(), "hMSetAsync should return CompletableFuture");

        Method hMSetAsyncMethod2 = clientClass.getMethod("hMSetAsync", String.class, Map.class, boolean.class);
        assertNotNull(hMSetAsyncMethod2, "hMSetAsync(String, Map, boolean) method should exist");
        assertEquals(CompletableFuture.class, hMSetAsyncMethod2.getReturnType(), "hMSetAsync with skipReplication should return CompletableFuture");

        // Test HEXISTS async method signature
        Method hExistsAsyncMethod = clientClass.getMethod("hExistsAsync", String.class, String.class);
        assertNotNull(hExistsAsyncMethod, "hExistsAsync(String, String) method should exist");
        assertEquals(CompletableFuture.class, hExistsAsyncMethod.getReturnType(), "hExistsAsync should return CompletableFuture");

        // Test EXISTS async method signature
        Method existsAsyncMethod = clientClass.getMethod("existsAsync", String.class);
        assertNotNull(existsAsyncMethod, "existsAsync(String) method should exist");
        assertEquals(CompletableFuture.class, existsAsyncMethod.getReturnType(), "existsAsync should return CompletableFuture");

        // Test SETNX async method signatures
        Method setNXAsyncMethod1 = clientClass.getMethod("setNXAsync", String.class, String.class);
        assertNotNull(setNXAsyncMethod1, "setNXAsync(String, String) method should exist");
        assertEquals(CompletableFuture.class, setNXAsyncMethod1.getReturnType(), "setNXAsync should return CompletableFuture");

        Method setNXAsyncMethod2 = clientClass.getMethod("setNXAsync", String.class, String.class, boolean.class);
        assertNotNull(setNXAsyncMethod2, "setNXAsync(String, String, boolean) method should exist");
        assertEquals(CompletableFuture.class, setNXAsyncMethod2.getReturnType(), "setNXAsync with skipReplication should return CompletableFuture");

        // Test LoadScript async method signature
        Method loadScriptAsyncMethod = clientClass.getMethod("loadScriptAsync", String.class);
        assertNotNull(loadScriptAsyncMethod, "loadScriptAsync(String) method should exist");
        assertEquals(CompletableFuture.class, loadScriptAsyncMethod.getReturnType(), "loadScriptAsync should return CompletableFuture");

        // Test EvalSha async method signatures
        Method evalShaAsyncMethod1 = clientClass.getMethod("evalShaAsync", String.class, List.class, List.class);
        assertNotNull(evalShaAsyncMethod1, "evalShaAsync(String, List, List) method should exist");
        assertEquals(CompletableFuture.class, evalShaAsyncMethod1.getReturnType(), "evalShaAsync should return CompletableFuture");

        Method evalShaAsyncMethod2 = clientClass.getMethod("evalShaAsync", String.class, List.class, List.class, boolean.class);
        assertNotNull(evalShaAsyncMethod2, "evalShaAsync(String, List, List, boolean) method should exist");
        assertEquals(CompletableFuture.class, evalShaAsyncMethod2.getReturnType(), "evalShaAsync with skipReplication should return CompletableFuture");

        // Test HealthCheck async method signature
        Method healthCheckAsyncMethod = clientClass.getMethod("healthCheckAsync");
        assertNotNull(healthCheckAsyncMethod, "healthCheckAsync() method should exist");
        assertEquals(CompletableFuture.class, healthCheckAsyncMethod.getReturnType(), "healthCheckAsync should return CompletableFuture");

        // Test Ping async method signature
        Method pingAsyncMethod = clientClass.getMethod("pingAsync");
        assertNotNull(pingAsyncMethod, "pingAsync() method should exist");
        assertEquals(CompletableFuture.class, pingAsyncMethod.getReturnType(), "pingAsync should return CompletableFuture");

        // Test basic async methods
        Method setAsyncMethod = clientClass.getMethod("setAsync", String.class, String.class);
        assertNotNull(setAsyncMethod, "setAsync(String, String) method should exist");
        assertEquals(CompletableFuture.class, setAsyncMethod.getReturnType(), "setAsync should return CompletableFuture");

        Method getAsyncMethod = clientClass.getMethod("getAsync", String.class);
        assertNotNull(getAsyncMethod, "getAsync(String) method should exist");
        assertEquals(CompletableFuture.class, getAsyncMethod.getReturnType(), "getAsync should return CompletableFuture");

        Method deleteAsyncMethod = clientClass.getMethod("deleteAsync", String.class);
        assertNotNull(deleteAsyncMethod, "deleteAsync(String) method should exist");
        assertEquals(CompletableFuture.class, deleteAsyncMethod.getReturnType(), "deleteAsync should return CompletableFuture");
    }

    @Test
    @DisplayName("Test BatchOperationBuilder new methods exist")
    void testBatchOperationBuilderMethods() throws Exception {
        Class<BatchOperationBuilder> builderClass = BatchOperationBuilder.class;

        // Test addHMSet method signature
        Method addHMSetMethod = builderClass.getMethod("addHMSet", String.class, Map.class);
        assertNotNull(addHMSetMethod, "addHMSet(String, Map) method should exist");
        assertEquals(BatchOperationBuilder.class, addHMSetMethod.getReturnType(), "addHMSet should return BatchOperationBuilder");

        // Test addSetNX method signature
        Method addSetNXMethod = builderClass.getMethod("addSetNX", String.class, String.class);
        assertNotNull(addSetNXMethod, "addSetNX(String, String) method should exist");
        assertEquals(BatchOperationBuilder.class, addSetNXMethod.getReturnType(), "addSetNX should return BatchOperationBuilder");

        // Test addLoadScript method signature
        Method addLoadScriptMethod = builderClass.getMethod("addLoadScript", String.class);
        assertNotNull(addLoadScriptMethod, "addLoadScript(String) method should exist");
        assertEquals(BatchOperationBuilder.class, addLoadScriptMethod.getReturnType(), "addLoadScript should return BatchOperationBuilder");

        // Test addEvalSha method signature
        Method addEvalShaMethod = builderClass.getMethod("addEvalSha", String.class, List.class, List.class);
        assertNotNull(addEvalShaMethod, "addEvalSha(String, List, List) method should exist");
        assertEquals(BatchOperationBuilder.class, addEvalShaMethod.getReturnType(), "addEvalSha should return BatchOperationBuilder");
    }

    @Test
    @DisplayName("Test method implementation status")
    void testMethodImplementationStatus() {
        // This test verifies that the methods exist and can be called
        // It doesn't test actual functionality since that requires a server

        Class<RustyClusterClient> clientClass = RustyClusterClient.class;

        // Test that LoadScript returns null (temporary implementation)
        assertDoesNotThrow(() -> {
            // Just verify the method signature exists and can be called
            Method loadScriptMethod = clientClass.getMethod("loadScript", String.class);
            assertNotNull(loadScriptMethod);
        }, "LoadScript method should exist and be callable");

        // Test that EvalSha returns null (temporary implementation)
        assertDoesNotThrow(() -> {
            Method evalShaMethod = clientClass.getMethod("evalSha", String.class, List.class, List.class);
            assertNotNull(evalShaMethod);
        }, "EvalSha method should exist and be callable");

        // Test async versions in main client
        assertDoesNotThrow(() -> {
            Method loadScriptAsyncMethod = clientClass.getMethod("loadScriptAsync", String.class);
            assertNotNull(loadScriptAsyncMethod);
        }, "LoadScriptAsync method should exist and be callable in main client");

        assertDoesNotThrow(() -> {
            Method evalShaAsyncMethod = clientClass.getMethod("evalShaAsync", String.class, List.class, List.class);
            assertNotNull(evalShaAsyncMethod);
        }, "EvalShaAsync method should exist and be callable in main client");

        // Test that both sync and async methods coexist
        assertDoesNotThrow(() -> {
            Method setMethod = clientClass.getMethod("set", String.class, String.class);
            Method setAsyncMethod = clientClass.getMethod("setAsync", String.class, String.class);
            assertNotNull(setMethod);
            assertNotNull(setAsyncMethod);
        }, "Both sync and async set methods should exist in main client");

        assertDoesNotThrow(() -> {
            Method getMethod = clientClass.getMethod("get", String.class);
            Method getAsyncMethod = clientClass.getMethod("getAsync", String.class);
            assertNotNull(getMethod);
            assertNotNull(getAsyncMethod);
        }, "Both sync and async get methods should exist in main client");
    }
}
