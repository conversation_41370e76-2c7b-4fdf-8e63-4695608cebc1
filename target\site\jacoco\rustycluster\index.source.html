<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>rustycluster</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">rustycluster</span></div><h1>rustycluster</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">43,100 of 43,100</td><td class="ctr2">0%</td><td class="bar">3,645 of 3,645</td><td class="ctr2">0%</td><td class="ctr1">5,389</td><td class="ctr2">5,389</td><td class="ctr1">12,158</td><td class="ctr2">12,158</td><td class="ctr1">3,489</td><td class="ctr2">3,489</td><td class="ctr1">159</td><td class="ctr2">159</td></tr></tfoot><tbody><tr><td id="a1"><a href="Rustycluster.java.html" class="el_source">Rustycluster.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="42,049" alt="42,049"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="3,620" alt="3,620"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">5,246</td><td class="ctr2" id="g0">5,246</td><td class="ctr1" id="h0">11,860</td><td class="ctr2" id="i0">11,860</td><td class="ctr1" id="j0">3,370</td><td class="ctr2" id="k0">3,370</td><td class="ctr1" id="l0">147</td><td class="ctr2" id="m0">147</td></tr><tr><td id="a0"><a href="KeyValueServiceGrpc.java.html" class="el_source">KeyValueServiceGrpc.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1,051" alt="1,051"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">143</td><td class="ctr2" id="g1">143</td><td class="ctr1" id="h1">298</td><td class="ctr2" id="i1">298</td><td class="ctr1" id="j1">119</td><td class="ctr2" id="k1">119</td><td class="ctr1" id="l1">12</td><td class="ctr2" id="m1">12</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>