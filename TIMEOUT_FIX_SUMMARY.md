# gRPC Timeout Fix Summary

## Problem Description

The RustyCluster Java client was experiencing `DEADLINE_EXCEEDED` errors with the message:
```
io.grpc.StatusRuntimeException: DEADLINE_EXCEEDED: ClientCall started after CallOptions deadline was exceeded -4.312434300 seconds ago.
```

This error occurred when using the library as a dependency in other projects, even though the example code worked fine.

## Root Cause Analysis

The issue was caused by setting gRPC deadlines at **stub creation time** rather than **per-operation**:

1. **Original Implementation**: gRPC stubs were created with `withDeadlineAfter(connectionTimeoutMs)` in the `StubFactory.create()` method
2. **Problem**: When stubs were pooled and reused, the deadline was already counting down from when the stub was first created
3. **Result**: By the time an operation was executed, the deadline had already expired, causing `DEADLINE_EXCEEDED` errors

## Solution Implemented

### 1. Removed Deadline from Stub Creation
- **File**: `ConnectionPool.java` and `AsyncConnectionPool.java`
- **Change**: Removed `withDeadlineAfter()` calls from stub factory methods
- **Reason**: Stubs should not have pre-set deadlines that start counting immediately

### 2. Added Per-Operation Timeout Support
- **File**: `ConnectionManager.java` and `AsyncConnectionManager.java`
- **Change**: Added overloaded `executeWithFailover` methods that accept `OperationType`
- **Implementation**: Apply deadline per operation using `stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS)`

### 3. Created Operation Type Classification
- **File**: `OperationType.java` (new)
- **Types**:
  - `READ`: Uses `readTimeoutMs` (get, hGet, hGetAll operations)
  - `WRITE`: Uses `writeTimeoutMs` (set, delete, hSet, increment operations)
  - `AUTH`: Uses `connectionTimeoutMs` (authentication operations)

### 4. Updated Client Methods
- **File**: `RustyClusterClient.java`
- **Change**: Updated all operation methods to specify the appropriate `OperationType`
- **Examples**:
  - `get()` operations use `OperationType.READ`
  - `set()`, `delete()`, `hSet()` operations use `OperationType.WRITE`
  - `authenticate()` operations use `OperationType.AUTH`

## Key Benefits

1. **Eliminates Deadline Exceeded Errors**: Deadlines are now set fresh for each operation
2. **Proper Timeout Differentiation**: Different operation types use appropriate timeouts
3. **Backward Compatibility**: Existing code continues to work without changes
4. **Performance Optimization**: Read operations can have different timeouts than write operations

## Technical Details

### Before (Problematic)
```java
// In StubFactory.create()
return KeyValueServiceGrpc.newBlockingStub(channel)
    .withDeadlineAfter(config.getConnectionTimeoutMs(), TimeUnit.MILLISECONDS);
```

### After (Fixed)
```java
// In StubFactory.create() - no deadline set
return KeyValueServiceGrpc.newBlockingStub(channel);

// In executeWithFailover() - deadline set per operation
long timeoutMs = switch (operationType) {
    case READ -> config.getReadTimeoutMs();
    case WRITE -> config.getWriteTimeoutMs();
    case AUTH -> config.getConnectionTimeoutMs();
};
KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline = 
    stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);
```

## Files Modified

1. `src/main/java/com/rustycluster/client/connection/OperationType.java` (new)
2. `src/main/java/com/rustycluster/client/connection/ConnectionPool.java`
3. `src/main/java/com/rustycluster/client/connection/AsyncConnectionPool.java`
4. `src/main/java/com/rustycluster/client/connection/ConnectionManager.java`
5. `src/main/java/com/rustycluster/client/connection/AsyncConnectionManager.java`
6. `src/main/java/com/rustycluster/client/RustyClusterClient.java`

## Testing

A comprehensive test suite has been added in `TimeoutFixTest.java` to verify:
- Read operations use read timeout
- Write operations use write timeout
- Auth operations use connection timeout
- Deadlines are applied per-operation, not per-stub

## Usage

The fix is transparent to existing users. No code changes are required in client applications. The library will now:

1. Apply appropriate timeouts based on operation type
2. Set fresh deadlines for each operation
3. Eliminate the "deadline exceeded" errors caused by stale stub deadlines

## Configuration Recommendations

For optimal performance, consider configuring different timeouts:

```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("localhost:50051", "localhost:50052", "localhost:50053")
    .connectionTimeout(5, TimeUnit.SECONDS)  // For auth operations
    .readTimeout(3, TimeUnit.SECONDS)        // For read operations
    .writeTimeout(2, TimeUnit.SECONDS)       // For write operations
    .maxRetries(3)
    .retryDelay(500, TimeUnit.MILLISECONDS)
    .build();
```

This fix ensures reliable operation in high-throughput environments and eliminates the timeout-related issues that were preventing successful library usage.
