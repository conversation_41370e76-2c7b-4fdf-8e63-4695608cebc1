<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterAsyncClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client</a> &gt; <span class="el_source">RustyClusterAsyncClient.java</span></div><h1>RustyClusterAsyncClient.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.AsyncConnectionManager;
import org.npci.rustyclient.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Asynchronous client for interacting with RustyCluster.
 * This client provides non-blocking operations for high-throughput scenarios.
 */
public class RustyClusterAsyncClient implements AutoCloseable {
<span class="fc" id="L19">    private static final Logger logger = LoggerFactory.getLogger(RustyClusterAsyncClient.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionManager connectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterAsyncClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="fc" id="L30">    public RustyClusterAsyncClient(RustyClusterClientConfig config) {</span>
<span class="fc" id="L31">        this.config = config;</span>
<span class="fc" id="L32">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="fc" id="L33">        this.connectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="fc" id="L34">        logger.info(&quot;RustyClusterAsyncClient initialized&quot;);</span>
<span class="fc" id="L35">    }</span>

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value, boolean skipReplication) {
<span class="fc" id="L46">        logger.debug(&quot;Setting key asynchronously: {}&quot;, key);</span>
<span class="fc" id="L47">        RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()</span>
<span class="fc" id="L48">                .setKey(key)</span>
<span class="fc" id="L49">                .setValue(value)</span>
<span class="fc" id="L50">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L51">                .build();</span>

<span class="fc" id="L53">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="fc" id="L54">                stub.set(request))</span>
<span class="fc" id="L55">                .thenApply(RustyClusterProto.SetResponse::getSuccess);</span>
    }

    /**
     * Set a key-value pair asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value) {
<span class="fc" id="L66">        return setAsync(key, value, false);</span>
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture&lt;String&gt; getAsync(String key) {
<span class="fc" id="L76">        logger.debug(&quot;Getting key asynchronously: {}&quot;, key);</span>
<span class="fc" id="L77">        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="fc" id="L78">                .setKey(key)</span>
<span class="fc" id="L79">                .build();</span>

<span class="fc" id="L81">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="fc" id="L82">                stub.get(request))</span>
<span class="pc bnc" id="L83" title="All 2 branches missed.">                .thenApply(response -&gt; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key, boolean skipReplication) {
<span class="nc" id="L94">        logger.debug(&quot;Deleting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L95">        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()</span>
<span class="nc" id="L96">                .setKey(key)</span>
<span class="nc" id="L97">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L98">                .build();</span>

<span class="nc" id="L100">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L101">                stub.delete(request))</span>
<span class="nc" id="L102">                .thenApply(RustyClusterProto.DeleteResponse::getSuccess);</span>
    }

    /**
     * Delete a key asynchronously with default replication.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key) {
<span class="nc" id="L112">        return deleteAsync(key, false);</span>
    }

    /**
     * Execute a batch of operations asynchronously.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;RustyClusterProto.BatchOperation&gt; operations, boolean skipReplication) {
<span class="nc" id="L123">        logger.debug(&quot;Executing batch write asynchronously with {} operations&quot;, operations.size());</span>
<span class="nc" id="L124">        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()</span>
<span class="nc" id="L125">                .addAllOperations(operations)</span>
<span class="nc" id="L126">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L127">                .build();</span>

<span class="nc" id="L129">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L130">                stub.batchWrite(request))</span>
<span class="nc" id="L131">                .thenApply(RustyClusterProto.BatchWriteResponse::getOperationResultsList);</span>
    }

    /**
     * Execute a batch of operations asynchronously with default replication.
     *
     * @param operations The list of operations to execute
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;RustyClusterProto.BatchOperation&gt; operations) {
<span class="nc" id="L141">        return batchWriteAsync(operations, false);</span>
    }

    /**
     * Increment a numeric value asynchronously.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value, boolean skipReplication) {
<span class="nc" id="L153">        logger.debug(&quot;Incrementing key asynchronously: {} by {}&quot;, key, value);</span>
<span class="nc" id="L154">        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()</span>
<span class="nc" id="L155">                .setKey(key)</span>
<span class="nc" id="L156">                .setValue(value)</span>
<span class="nc" id="L157">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L158">                .build();</span>

<span class="nc" id="L160">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L161">                stub.incrBy(request))</span>
<span class="nc" id="L162">                .thenApply(RustyClusterProto.IncrByResponse::getNewValue);</span>
    }

    /**
     * Increment a numeric value asynchronously with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value) {
<span class="nc" id="L173">        return incrByAsync(key, value, false);</span>
    }

    /**
     * Get all fields from a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture&lt;Map&lt;String, String&gt;&gt; hGetAllAsync(String key) {
<span class="nc" id="L183">        logger.debug(&quot;Getting all hash fields asynchronously for key: {}&quot;, key);</span>
<span class="nc" id="L184">        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()</span>
<span class="nc" id="L185">                .setKey(key)</span>
<span class="nc" id="L186">                .build();</span>

<span class="nc" id="L188">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L189">                stub.hGetAll(request))</span>
<span class="nc" id="L190">                .thenApply(RustyClusterProto.HGetAllResponse::getFieldsMap);</span>
    }

    /**
     * Authenticate with the RustyCluster server asynchronously.
     *
     * @return CompletableFuture that completes with true if authentication was successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; authenticateAsync() {
<span class="nc" id="L199">        logger.debug(&quot;Attempting to authenticate asynchronously with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L201" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L202">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L203">            return CompletableFuture.completedFuture(true);</span>
        }

        // For async authentication, we need to use a different approach
        // since authenticate method expects a blocking stub
<span class="nc" id="L208">        return CompletableFuture.supplyAsync(() -&gt; {</span>
            try {
                // This is a simplified approach - in a real implementation,
                // you'd want to create an async version of authenticate
<span class="nc" id="L212">                return authenticationManager.isAuthenticated();</span>
<span class="nc" id="L213">            } catch (Exception e) {</span>
<span class="nc" id="L214">                logger.error(&quot;Authentication check failed&quot;, e);</span>
<span class="nc" id="L215">                return false;</span>
            }
        });
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="nc" id="L226">        return authenticationManager.isAuthenticated();</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="nc" id="L235">        return authenticationManager.getSessionToken();</span>
    }

    // ==================== NEW ASYNC METHODS ====================

    /**
     * Set multiple fields in a hash asynchronously.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields, boolean skipReplication) {
<span class="fc" id="L249">        logger.debug(&quot;Setting multiple hash fields asynchronously for key: {}, fields count: {}&quot;, key, fields.size());</span>

        // For now, we'll implement this using individual hSet calls until gRPC classes are regenerated
        // This is a temporary implementation
<span class="fc" id="L253">        List&lt;CompletableFuture&lt;Boolean&gt;&gt; futures = fields.entrySet().stream()</span>
<span class="fc" id="L254">                .map(entry -&gt; hSetAsync(key, entry.getKey(), entry.getValue(), skipReplication))</span>
<span class="fc" id="L255">                .toList();</span>

<span class="fc" id="L257">        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))</span>
<span class="pc" id="L258">                .thenApply(v -&gt; futures.stream().allMatch(CompletableFuture::join));</span>
    }

    /**
     * Set multiple fields in a hash asynchronously with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields) {
<span class="fc" id="L269">        return hMSetAsync(key, fields, false);</span>
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value, boolean skipReplication) {
<span class="fc" id="L282">        logger.debug(&quot;Setting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="fc" id="L283">        RustyClusterProto.HSetRequest request = RustyClusterProto.HSetRequest.newBuilder()</span>
<span class="fc" id="L284">                .setKey(key)</span>
<span class="fc" id="L285">                .setField(field)</span>
<span class="fc" id="L286">                .setValue(value)</span>
<span class="fc" id="L287">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L288">                .build();</span>

<span class="fc" id="L290">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="fc" id="L291">                stub.hSet(request))</span>
<span class="fc" id="L292">                .thenApply(RustyClusterProto.HSetResponse::getSuccess);</span>
    }

    /**
     * Set a hash field asynchronously with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value) {
<span class="fc" id="L304">        return hSetAsync(key, field, value, false);</span>
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found
     */
    public CompletableFuture&lt;String&gt; hGetAsync(String key, String field) {
<span class="nc" id="L315">        logger.debug(&quot;Getting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="nc" id="L316">        RustyClusterProto.HGetRequest request = RustyClusterProto.HGetRequest.newBuilder()</span>
<span class="nc" id="L317">                .setKey(key)</span>
<span class="nc" id="L318">                .setField(field)</span>
<span class="nc" id="L319">                .build();</span>

<span class="nc" id="L321">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L322">                stub.hGet(request))</span>
<span class="nc bnc" id="L323" title="All 2 branches missed.">                .thenApply(response -&gt; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Check if a hash field exists asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with true if field exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hExistsAsync(String key, String field) {
<span class="nc" id="L334">        logger.debug(&quot;Checking if hash field exists asynchronously: {}.{}&quot;, key, field);</span>

        // Temporary implementation using hGet until gRPC classes are regenerated
<span class="nc bnc" id="L337" title="All 2 branches missed.">        return hGetAsync(key, field).thenApply(value -&gt; value != null);</span>
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value, boolean skipReplication) {
<span class="fc" id="L349">        logger.debug(&quot;Setting key if not exists asynchronously: {}&quot;, key);</span>

        // Temporary implementation using exists check + set until gRPC classes are regenerated
<span class="fc" id="L352">        return existsAsync(key).thenCompose(exists -&gt; {</span>
<span class="nc bnc" id="L353" title="All 2 branches missed.">            if (exists) {</span>
<span class="nc" id="L354">                return CompletableFuture.completedFuture(false); // Key already exists</span>
            }
<span class="nc" id="L356">            return setAsync(key, value, skipReplication);</span>
        });
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value) {
<span class="fc" id="L368">        return setNXAsync(key, value, false);</span>
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if key exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; existsAsync(String key) {
<span class="fc" id="L378">        logger.debug(&quot;Checking if key exists asynchronously: {}&quot;, key);</span>

        // Temporary implementation using get until gRPC classes are regenerated
<span class="pc bnc" id="L381" title="All 2 branches missed.">        return getAsync(key).thenApply(value -&gt; value != null);</span>
    }

    /**
     * Load a Lua script and return its SHA hash asynchronously.
     *
     * @param script The Lua script to load
     * @return CompletableFuture that completes with the SHA hash of the loaded script, or null if failed
     */
    public CompletableFuture&lt;String&gt; loadScriptAsync(String script) {
<span class="fc" id="L391">        logger.debug(&quot;Loading script asynchronously, length: {}&quot;, script.length());</span>

        // Temporary implementation - return a mock SHA until gRPC classes are regenerated
        // In real implementation, this would call the LoadScript RPC
<span class="fc" id="L395">        logger.warn(&quot;loadScriptAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="fc" id="L396">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args, boolean skipReplication) {
<span class="fc" id="L409">        logger.debug(&quot;Executing script asynchronously with SHA: {}, keys: {}, args: {}&quot;, sha, keys.size(), args.size());</span>

        // Temporary implementation until gRPC classes are regenerated
<span class="fc" id="L412">        logger.warn(&quot;evalShaAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="fc" id="L413">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
<span class="fc" id="L425">        return evalShaAsync(sha, keys, args, false);</span>
    }

    /**
     * Perform a health check on the cluster asynchronously.
     *
     * @return CompletableFuture that completes with true if healthy, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; healthCheckAsync() {
<span class="fc" id="L434">        logger.debug(&quot;Performing health check asynchronously&quot;);</span>

        // Use ping for now until HealthCheck RPC is available
<span class="fc" id="L437">        return pingAsync().exceptionally(throwable -&gt; {</span>
<span class="nc" id="L438">            logger.warn(&quot;Health check failed: {}&quot;, throwable.getMessage());</span>
<span class="nc" id="L439">            return false;</span>
        });
    }

    /**
     * Ping the cluster to check connectivity asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; pingAsync() {
<span class="fc" id="L449">        logger.debug(&quot;Pinging cluster asynchronously&quot;);</span>

<span class="fc" id="L451">        RustyClusterProto.PingRequest request = RustyClusterProto.PingRequest.newBuilder().build();</span>
<span class="fc" id="L452">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="fc" id="L453">                stub.ping(request))</span>
<span class="fc" id="L454">                .thenApply(RustyClusterProto.PingResponse::getSuccess)</span>
<span class="fc" id="L455">                .exceptionally(throwable -&gt; {</span>
<span class="fc" id="L456">                    logger.warn(&quot;Ping failed: {}&quot;, throwable.getMessage());</span>
<span class="fc" id="L457">                    return false;</span>
                });
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
<span class="nc" id="L466">        authenticationManager.clearAuthentication();</span>
<span class="nc" id="L467">        connectionManager.close();</span>
<span class="nc" id="L468">        logger.info(&quot;RustyClusterAsyncClient closed&quot;);</span>
<span class="nc" id="L469">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>