<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchOperationBuilder.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client</a> &gt; <span class="el_source">BatchOperationBuilder.java</span></div><h1>BatchOperationBuilder.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.npci.rustyclient.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Builder for creating batch operations.
 */
public class BatchOperationBuilder {
<span class="fc" id="L15">    private static final Logger logger = LoggerFactory.getLogger(BatchOperationBuilder.class);</span>
<span class="fc" id="L16">    private final List&lt;RustyClusterProto.BatchOperation&gt; operations = new ArrayList&lt;&gt;();</span>

    /**
     * Create a new BatchOperationBuilder.
     */
<span class="fc" id="L21">    public BatchOperationBuilder() {</span>
<span class="fc" id="L22">    }</span>

    /**
     * Add a SET operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @return The builder instance
     */
    public BatchOperationBuilder addSet(String key, String value) {
<span class="fc" id="L32">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L33">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SET)</span>
<span class="fc" id="L34">                .setKey(key)</span>
<span class="fc" id="L35">                .setValue(value)</span>
<span class="fc" id="L36">                .build();</span>
<span class="fc" id="L37">        operations.add(operation);</span>
<span class="fc" id="L38">        return this;</span>
    }

    /**
     * Add a DELETE operation to the batch.
     *
     * @param key The key
     * @return The builder instance
     */
    public BatchOperationBuilder addDelete(String key) {
<span class="fc" id="L48">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L49">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DELETE)</span>
<span class="fc" id="L50">                .setKey(key)</span>
<span class="fc" id="L51">                .build();</span>
<span class="fc" id="L52">        operations.add(operation);</span>
<span class="fc" id="L53">        return this;</span>
    }

    /**
     * Add a SETEX operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetEx(String key, String value, long ttl) {
<span class="fc" id="L65">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L66">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEX)</span>
<span class="fc" id="L67">                .setKey(key)</span>
<span class="fc" id="L68">                .setValue(value)</span>
<span class="fc" id="L69">                .setTtl(ttl)</span>
<span class="fc" id="L70">                .build();</span>
<span class="fc" id="L71">        operations.add(operation);</span>
<span class="fc" id="L72">        return this;</span>
    }

    /**
     * Add a SETEXPIRY operation to the batch.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetExpiry(String key, long ttl) {
<span class="fc" id="L83">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L84">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEXPIRY)</span>
<span class="fc" id="L85">                .setKey(key)</span>
<span class="fc" id="L86">                .setTtl(ttl)</span>
<span class="fc" id="L87">                .build();</span>
<span class="fc" id="L88">        operations.add(operation);</span>
<span class="fc" id="L89">        return this;</span>
    }

    /**
     * Add an INCRBY operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrBy(String key, long value) {
<span class="fc" id="L100">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L101">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBY)</span>
<span class="fc" id="L102">                .setKey(key)</span>
<span class="fc" id="L103">                .setIntValue(value)</span>
<span class="fc" id="L104">                .build();</span>
<span class="fc" id="L105">        operations.add(operation);</span>
<span class="fc" id="L106">        return this;</span>
    }

    /**
     * Add a DECRBY operation to the batch.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addDecrBy(String key, long value) {
<span class="fc" id="L117">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L118">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DECRBY)</span>
<span class="fc" id="L119">                .setKey(key)</span>
<span class="fc" id="L120">                .setIntValue(value)</span>
<span class="fc" id="L121">                .build();</span>
<span class="fc" id="L122">        operations.add(operation);</span>
<span class="fc" id="L123">        return this;</span>
    }

    /**
     * Add an INCRBYFLOAT operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrByFloat(String key, double value) {
<span class="fc" id="L134">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L135">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBYFLOAT)</span>
<span class="fc" id="L136">                .setKey(key)</span>
<span class="fc" id="L137">                .setFloatValue(value)</span>
<span class="fc" id="L138">                .build();</span>
<span class="fc" id="L139">        operations.add(operation);</span>
<span class="fc" id="L140">        return this;</span>
    }

    /**
     * Add an HSET operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return The builder instance
     */
    public BatchOperationBuilder addHSet(String key, String field, String value) {
<span class="fc" id="L152">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L153">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HSET)</span>
<span class="fc" id="L154">                .setKey(key)</span>
<span class="fc" id="L155">                .setField(field)</span>
<span class="fc" id="L156">                .setValue(value)</span>
<span class="fc" id="L157">                .build();</span>
<span class="fc" id="L158">        operations.add(operation);</span>
<span class="fc" id="L159">        return this;</span>
    }

    /**
     * Add an HINCRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrBy(String key, String field, long value) {
<span class="fc" id="L171">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L172">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBY)</span>
<span class="fc" id="L173">                .setKey(key)</span>
<span class="fc" id="L174">                .setField(field)</span>
<span class="fc" id="L175">                .setIntValue(value)</span>
<span class="fc" id="L176">                .build();</span>
<span class="fc" id="L177">        operations.add(operation);</span>
<span class="fc" id="L178">        return this;</span>
    }

    /**
     * Add an HDECRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addHDecrBy(String key, String field, long value) {
<span class="fc" id="L190">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L191">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HDECRBY)</span>
<span class="fc" id="L192">                .setKey(key)</span>
<span class="fc" id="L193">                .setField(field)</span>
<span class="fc" id="L194">                .setIntValue(value)</span>
<span class="fc" id="L195">                .build();</span>
<span class="fc" id="L196">        operations.add(operation);</span>
<span class="fc" id="L197">        return this;</span>
    }

    /**
     * Add an HINCRBYFLOAT operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrByFloat(String key, String field, double value) {
<span class="fc" id="L209">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L210">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBYFLOAT)</span>
<span class="fc" id="L211">                .setKey(key)</span>
<span class="fc" id="L212">                .setField(field)</span>
<span class="fc" id="L213">                .setFloatValue(value)</span>
<span class="fc" id="L214">                .build();</span>
<span class="fc" id="L215">        operations.add(operation);</span>
<span class="fc" id="L216">        return this;</span>
    }

    // ==================== NEW BATCH OPERATIONS ====================

    /**
     * Add an HMSET operation to the batch.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return The builder instance
     */
    public BatchOperationBuilder addHMSet(String key, Map&lt;String, String&gt; fields) {
        // For now, add individual HSET operations until HMSET is available in BatchOperation
<span class="fc bfc" id="L230" title="All 2 branches covered.">        for (Map.Entry&lt;String, String&gt; entry : fields.entrySet()) {</span>
<span class="fc" id="L231">            addHSet(key, entry.getKey(), entry.getValue());</span>
<span class="fc" id="L232">        }</span>
<span class="fc" id="L233">        return this;</span>
    }

    /**
     * Add a SETNX operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @return The builder instance
     */
    public BatchOperationBuilder addSetNX(String key, String value) {
        // Note: This will be implemented properly when SETNX is available in BatchOperation.OperationType
        // For now, we'll use a regular SET operation as a placeholder
<span class="fc" id="L246">        logger.warn(&quot;SETNX batch operation not fully implemented yet - using SET as placeholder&quot;);</span>
<span class="fc" id="L247">        return addSet(key, value);</span>
    }

    /**
     * Add a LOAD_SCRIPT operation to the batch.
     *
     * @param script The Lua script to load
     * @return The builder instance
     */
    public BatchOperationBuilder addLoadScript(String script) {
        // Note: This will be implemented when LOAD_SCRIPT is available in BatchOperation.OperationType
<span class="fc" id="L258">        logger.warn(&quot;LOAD_SCRIPT batch operation not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="fc" id="L259">        return this;</span>
    }

    /**
     * Add an EVALSHA operation to the batch.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return The builder instance
     */
    public BatchOperationBuilder addEvalSha(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
        // Note: This will be implemented when EVALSHA is available in BatchOperation.OperationType
<span class="fc" id="L272">        logger.warn(&quot;EVALSHA batch operation not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="fc" id="L273">        return this;</span>
    }

    /**
     * Build the list of batch operations.
     *
     * @return The list of batch operations
     */
    public List&lt;RustyClusterProto.BatchOperation&gt; build() {
<span class="fc" id="L282">        return new ArrayList&lt;&gt;(operations);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>