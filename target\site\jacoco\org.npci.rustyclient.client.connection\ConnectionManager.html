<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConnectionManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_class">ConnectionManager</span></div><h1>ConnectionManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">43 of 430</td><td class="ctr2">90%</td><td class="bar">14 of 60</td><td class="ctr2">76%</td><td class="ctr1">13</td><td class="ctr2">44</td><td class="ctr1">10</td><td class="ctr2">105</td><td class="ctr1">0</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a4"><a href="ConnectionManager.java.html#L85" class="el_method">executeWithFailover(ConnectionManager.ClientOperation, OperationType)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="27" alt="27"/><img src="../jacoco-resources/greenbar.gif" width="103" height="10" title="164" alt="164"/></td><td class="ctr2" id="c10">85%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="107" height="10" title="25" alt="25"/></td><td class="ctr2" id="e3">89%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g0">16</td><td class="ctr1" id="h0">7</td><td class="ctr2" id="i0">49</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="ConnectionManager.java.html#L233" class="el_method">isAuthenticationError(Exception)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="37" alt="37"/></td><td class="ctr2" id="c12">78%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="9" alt="9"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="ConnectionManager.java.html#L175" class="el_method">findNextAvailableNode(NodeConfig)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="46" alt="46"/></td><td class="ctr2" id="c9">92%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="e4">75%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i1">17</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="ConnectionManager.java.html#L176" class="el_method">lambda$findNextAvailableNode$1(NodeConfig, NodeConfig)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="11" alt="11"/></td><td class="ctr2" id="c11">84%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="e5">75%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="ConnectionManager.java.html#L44" class="el_method">ConnectionManager(RustyClusterClientConfig, ConnectionPool)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="49" alt="49"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="ConnectionManager.java.html#L209" class="el_method">isNodeAvailable(NodeConfig)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="29" alt="29"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a9"><a href="ConnectionManager.java.html#L186" class="el_method">lambda$findNextAvailableNode$2(NodeConfig, NodeConfig)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="11" alt="11"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="ConnectionManager.java.html#L258" class="el_method">close()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="10" alt="10"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="ConnectionManager.java.html#L35" class="el_method">ConnectionManager(RustyClusterClientConfig, AuthenticationManager)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="ConnectionManager.java.html#L196" class="el_method">lambda$findNextAvailableNode$3(NodeConfig, NodeConfig)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="ConnectionManager.java.html#L72" class="el_method">executeWithFailover(ConnectionManager.ClientOperation)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a11"><a href="ConnectionManager.java.html#L50" class="el_method">lambda$new$0(NodeConfig)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a12"><a href="ConnectionManager.java.html#L20" class="el_method">static {...}</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>