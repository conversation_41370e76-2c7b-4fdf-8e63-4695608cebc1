# RustyCluster Java Client - High-Throughput Performance Optimizations

This document outlines the comprehensive performance optimizations implemented for high-throughput scenarios.

## 🚀 Performance Optimizations Summary

### 1. **Connection Pool Optimizations**

#### **Before:**
- Conservative pool settings (10 connections max)
- Expensive validation on borrow/return
- Basic eviction strategy

#### **After:**
- **Increased default connections**: 20 per node (up from 10)
- **Optimized validation**: Disabled on borrow/return, only validate idle connections
- **Fail-fast behavior**: No blocking when pool exhausted
- **Warm connections**: Keep 25% of connections warm (min idle)
- **JMX monitoring**: Production-ready metrics
- **Faster eviction**: 30-second intervals vs 1 minute

```java
// High-throughput pool configuration
poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());
poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm
poolConfig.setTestOnBorrow(false); // Disable for performance
poolConfig.setBlockWhenExhausted(false); // Fail fast
poolConfig.setMaxWait(java.time.Duration.ofMillis(100)); // Quick timeout
```

### 2. **gRPC Channel Optimizations**

#### **Before:**
- Basic keep-alive settings (30s/10s)
- 10MB message limit
- No connection lifecycle management

#### **After:**
- **Aggressive keep-alive**: 15s/5s (vs 30s/10s)
- **Larger message size**: 50MB (vs 10MB) for large batches
- **Connection lifecycle**: Automatic idle timeout and age limits
- **Built-in retries**: Enabled with 3 max attempts
- **Metadata optimization**: 8KB metadata limit

```java
// High-throughput channel configuration
.keepAliveTime(15, TimeUnit.SECONDS) // More aggressive
.keepAliveTimeout(5, TimeUnit.SECONDS) // Faster detection
.maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches
.idleTimeout(5, TimeUnit.MINUTES) // Close idle connections
.enableRetry().maxRetryAttempts(3) // Built-in retries
```

### 3. **Configuration Defaults Optimization**

#### **Before:**
- Conservative timeouts (5000ms)
- High retry delays (500ms)
- Limited connections (10)

#### **After:**
- **Faster timeouts**: 3000ms connection, 2000ms read/write (vs 5000ms)
- **Reduced retry delay**: 100ms (vs 500ms)
- **More connections**: 20 per node (vs 10)
- **Fewer retries**: 2 attempts (vs 3) for faster failure detection

### 4. **Performance Presets**

Added three optimized configuration presets:

#### **High-Throughput Preset**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("localhost:50051", "localhost:50052")
    .highThroughputPreset() // 50 connections, 1s timeouts, 1 retry
    .build();
```

#### **Low-Latency Preset**
```java
config.lowLatencyPreset(); // 10 connections, 500ms timeouts, 0 retries
```

#### **Balanced Preset**
```java
config.balancedPreset(); // 20 connections, 2-3s timeouts, 2 retries
```

### 5. **Unified Asynchronous Operations Support**

#### **Integrated Async Methods**
- `RustyClusterClient` supports both sync and async operations
- `CompletableFuture`-based API for async methods
- Shared connection pools for efficiency
- Optimized for concurrent workloads

```java
try (RustyClusterClient client = new RustyClusterClient(config)) {
    // Synchronous operation
    boolean syncResult = client.set("key", "value");

    // Asynchronous operation
    CompletableFuture<Boolean> asyncResult = client.setAsync("key", "value");
}
```

### 6. **Performance Monitoring & Metrics**

#### **Real-time Metrics Collection**
- Operation counters (total, success, failed, retries)
- Latency tracking (min, max, average)
- Connection pool metrics
- Failover event tracking
- Throughput calculation (ops/sec)

```java
// Get performance statistics
PerformanceMetrics.PerformanceStats stats = client.getPerformanceStats();
logger.info("Throughput: {:.2f} ops/sec, Avg Latency: {:.2f}ms", 
    stats.throughputOpsPerSec(), stats.avgLatencyMs());
```

### 7. **Batch Operation Optimizations**

#### **Enhanced Batch Processing**
- Support for mixed operation types in single batch
- Optimized protobuf message construction
- Reduced serialization overhead

```java
BatchOperationBuilder builder = new BatchOperationBuilder()
    .addSet("key1", "value1")
    .addIncrBy("counter", 1)
    .addHSet("hash", "field", "value");
    
List<Boolean> results = client.batchWrite(builder.build());
```

## 📊 Performance Benchmarks

### **Expected Performance Improvements**

| Metric | Before | After | Improvement |
|--------|--------|-------|-------------|
| **Max Connections** | 10/node | 20-50/node | 2-5x |
| **Connection Timeout** | 5000ms | 1000-3000ms | 40-67% faster |
| **Retry Delay** | 500ms | 50-100ms | 80-90% faster |
| **Message Size** | 10MB | 50MB | 5x larger batches |
| **Keep-alive** | 30s/10s | 15s/5s | 2x more responsive |

### **Throughput Scenarios**

#### **High-Throughput Scenario**
```java
// Configuration for maximum throughput
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("node1:50051", "node2:50051", "node3:50051")
    .highThroughputPreset()
    .build();

// Expected: 10,000+ ops/sec with proper server capacity
```

#### **Low-Latency Scenario**
```java
// Configuration for minimal latency
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("localhost:50051")
    .lowLatencyPreset()
    .build();

// Expected: <1ms average latency for local operations
```

## 🛠️ Usage Examples

### **Synchronous High-Throughput**
```java
try (RustyClusterClient client = new RustyClusterClient(config)) {
    // Batch operations for maximum throughput
    BatchOperationBuilder builder = new BatchOperationBuilder();
    for (int i = 0; i < 1000; i++) {
        builder.addSet("key_" + i, "value_" + i);
    }
    
    List<Boolean> results = client.batchWrite(builder.build());
    client.logPerformanceStats(); // Monitor performance
}
```

### **Asynchronous Concurrent Operations**
```java
try (RustyClusterClient client = new RustyClusterClient(config)) {
    List<CompletableFuture<Boolean>> futures = new ArrayList<>();

    // Launch 10,000 concurrent operations
    for (int i = 0; i < 10000; i++) {
        futures.add(client.setAsync("key_" + i, "value_" + i));
    }

    // Wait for all to complete
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
}
```

### **Multi-threaded Operations**
```java
ExecutorService executor = Executors.newFixedThreadPool(10);
try (RustyClusterClient client = new RustyClusterClient(config)) {
    
    List<CompletableFuture<Void>> threadFutures = new ArrayList<>();
    
    for (int thread = 0; thread < 10; thread++) {
        final int threadId = thread;
        threadFutures.add(CompletableFuture.runAsync(() -> {
            for (int op = 0; op < 1000; op++) {
                client.set("thread_" + threadId + "_key_" + op, "value_" + op);
            }
        }, executor));
    }
    
    CompletableFuture.allOf(threadFutures.toArray(new CompletableFuture[0])).join();
    client.logPerformanceStats();
}
```

## 🔧 Configuration Recommendations

### **For Maximum Throughput**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("node1:50051", "node2:50051", "node3:50051")
    .highThroughputPreset()
    .maxConnectionsPerNode(50) // Even more connections if needed
    .build();
```

### **For Low Latency**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addPrimaryNode("localhost", 50051)
    .lowLatencyPreset()
    .maxConnectionsPerNode(5) // Fewer connections for lower latency
    .build();
```

### **For Production Environments**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("primary:50051", "secondary:50051", "tertiary:50051")
    .balancedPreset() // Good balance of performance and reliability
    .useSecureConnection("/path/to/cert.pem") // TLS in production
    .authentication("username", "password") // Authentication
    .build();
```

## 📈 Monitoring & Observability

### **JMX Metrics**
- Connection pool metrics available via JMX
- Monitor active/idle connections
- Track pool performance

### **Application Metrics**
```java
// Log performance stats periodically
PerformanceMetrics.PerformanceStats stats = client.getPerformanceStats();
logger.info("Performance: {} ops, {:.2f}% success, {:.2f} ops/sec", 
    stats.totalOperations(), stats.successRate(), stats.throughputOpsPerSec());
```

### **Health Checks**
- Built-in connection validation
- Automatic failover monitoring
- Node availability tracking

## 🎯 Best Practices

1. **Use appropriate presets** based on your use case
2. **Monitor performance metrics** in production
3. **Use batch operations** for bulk data operations
4. **Consider async client** for high-concurrency scenarios
5. **Tune connection pools** based on your server capacity
6. **Enable JMX monitoring** for production observability
7. **Use multiple nodes** for high availability and load distribution

## 🔍 Troubleshooting

### **Low Throughput**
- Increase `maxConnectionsPerNode`
- Use `highThroughputPreset()`
- Consider async operations
- Check server capacity

### **High Latency**
- Use `lowLatencyPreset()`
- Reduce connection pool size
- Check network latency
- Consider local deployment

### **Connection Issues**
- Monitor JMX metrics
- Check connection pool settings
- Verify network connectivity
- Review server logs
