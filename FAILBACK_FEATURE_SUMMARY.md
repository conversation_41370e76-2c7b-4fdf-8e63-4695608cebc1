# Automatic Failback Feature

## Overview

The RustyCluster Java client now includes an **automatic failback mechanism** that periodically checks if higher-priority nodes have recovered and automatically switches back to them when they become available.

## Problem Solved

**Before**: Once the client failed over to a secondary node, it would stay there permanently, even if the primary node recovered.

**After**: The client automatically monitors node health and fails back to higher-priority nodes when they become healthy again.

## Key Features

### 1. **Automatic Health Monitoring**
- Periodically checks if higher-priority nodes have recovered
- Configurable check interval (default: 30 seconds)
- Multiple health check retries before considering a node healthy

### 2. **Priority-Based Failback**
- Always tries to use the highest-priority available node
- PRIMARY → SECONDARY → TERTIARY priority order
- Only fails back to nodes with higher priority than the current node

### 3. **Configurable Behavior**
- Enable/disable failback functionality
- Customize health check intervals
- Configure number of health check retries
- Works with both sync and async operations

### 4. **Non-Blocking Operation**
- Failback checks run in background threads
- No impact on application performance
- Graceful shutdown when client is closed

## Configuration

### Basic Configuration
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addPrimaryNode("primary-server", 50051)
    .addSecondaryNode("secondary-server", 50052)
    .addTertiaryNode("tertiary-server", 50053)
    .enableFailback(true)  // Enable automatic failback (default: true)
    .build();
```

### Advanced Configuration
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addPrimaryNode("primary-server", 50051)
    .addSecondaryNode("secondary-server", 50052)
    .addTertiaryNode("tertiary-server", 50053)
    
    // Failback configuration
    .enableFailback(true)                           // Enable failback
    .failbackCheckInterval(15, TimeUnit.SECONDS)    // Check every 15 seconds
    .failbackHealthCheckRetries(3)                  // Require 3 successful health checks
    
    // Other optimizations
    .readTimeout(2, TimeUnit.SECONDS)
    .writeTimeout(2, TimeUnit.SECONDS)
    .maxRetries(2)
    .build();
```

### Disable Failback
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("primary:50051", "secondary:50052", "tertiary:50053")
    .enableFailback(false)  // Disable automatic failback
    .build();
```

## How It Works

### 1. **Initial Connection**
- Client starts by connecting to the highest-priority node (PRIMARY)
- If PRIMARY is unavailable, fails over to SECONDARY, then TERTIARY

### 2. **Background Monitoring**
- FailbackManager runs in a background thread
- Periodically checks if higher-priority nodes are healthy
- Uses lightweight health check operations

### 3. **Health Check Process**
- Performs a simple GET operation on a non-existent key
- Requires multiple consecutive successful checks
- Uses short timeouts (1 second) to avoid blocking

### 4. **Automatic Failback**
- When a higher-priority node becomes healthy, switches to it immediately
- Logs the failback operation for monitoring
- Continues normal operations on the new node

## Example Scenario

```
Initial State:
- PRIMARY (priority 0): HEALTHY → Client connects here
- SECONDARY (priority 1): HEALTHY
- TERTIARY (priority 2): HEALTHY

Primary Fails:
- PRIMARY (priority 0): UNHEALTHY
- SECONDARY (priority 1): HEALTHY → Client fails over here
- TERTIARY (priority 2): HEALTHY

Primary Recovers:
- PRIMARY (priority 0): HEALTHY ← Failback manager detects this
- SECONDARY (priority 1): HEALTHY
- TERTIARY (priority 2): HEALTHY

Result: Client automatically fails back to PRIMARY
```

## Configuration Options

| Option | Default | Description |
|--------|---------|-------------|
| `enableFailback` | `true` | Enable/disable automatic failback |
| `failbackCheckInterval` | `30 seconds` | How often to check for node recovery |
| `failbackHealthCheckRetries` | `2` | Number of successful checks required |

## Implementation Details

### Files Added/Modified

1. **New Files**:
   - `FailbackManager.java` - Handles sync failback operations
   - `AsyncFailbackManager.java` - Handles async failback operations
   - `FailbackManagerTest.java` - Comprehensive test suite

2. **Modified Files**:
   - `RustyClusterClientConfig.java` - Added failback configuration options
   - `ConnectionManager.java` - Integrated FailbackManager
   - `AsyncConnectionManager.java` - Integrated AsyncFailbackManager

### Health Check Strategy

The failback manager uses a lightweight health check strategy:

```java
// Performs a simple GET operation with short timeout
RustyClusterProto.GetRequest healthCheckRequest = 
    RustyClusterProto.GetRequest.newBuilder()
        .setKey("__health_check__")
        .build();

// Uses 1-second timeout for quick response
stub.withDeadlineAfter(1000, TimeUnit.MILLISECONDS)
    .get(healthCheckRequest);
```

## Benefits

### 1. **Improved Availability**
- Automatically returns to preferred nodes when they recover
- Reduces manual intervention requirements
- Maintains optimal performance by using primary nodes when available

### 2. **Load Distribution**
- Prevents permanent load concentration on secondary nodes
- Ensures primary nodes are utilized when healthy
- Better resource utilization across the cluster

### 3. **Operational Excellence**
- Reduces operational overhead
- Provides automatic recovery from temporary outages
- Maintains service quality during node maintenance

## Monitoring and Logging

The failback manager provides detailed logging:

```
INFO  - FailbackManager started with check interval: 30000ms
INFO  - Failing back from secondary:50052 (priority 1) to primary:50051 (priority 0)
DEBUG - Node primary:50051 passed 2/2 health checks
DEBUG - Health check failed for node secondary:50052: Connection refused
```

## Best Practices

### 1. **Choose Appropriate Check Intervals**
- **High-availability systems**: 10-15 seconds
- **Standard systems**: 30-60 seconds
- **Low-priority systems**: 2-5 minutes

### 2. **Configure Health Check Retries**
- **Stable networks**: 1-2 retries
- **Unstable networks**: 3-5 retries
- **Critical systems**: 2-3 retries (balance between reliability and speed)

### 3. **Monitor Failback Events**
- Log failback operations for operational visibility
- Set up alerts for frequent failbacks (may indicate network issues)
- Track node health patterns for capacity planning

## Backward Compatibility

- **Fully backward compatible** - existing code continues to work unchanged
- **Default enabled** - failback is enabled by default for new configurations
- **Graceful degradation** - if failback is disabled, behavior is identical to previous versions

## Testing

Comprehensive test suite includes:
- Failback to higher-priority nodes when they recover
- No failback when nodes remain unhealthy
- Proper handling when already on highest-priority node
- Respect for disabled failback configuration
- Multiple health check retry scenarios

The automatic failback feature significantly improves the resilience and operational characteristics of the RustyCluster Java client, ensuring optimal node utilization and reducing manual intervention requirements.
