<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">AsyncConnectionManager.java</span></div><h1>AsyncConnectionManager.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import com.google.common.util.concurrent.ListenableFuture;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.exception.NoAvailableNodesException;
import org.npci.rustyclient.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages asynchronous connections to RustyCluster nodes, handling prioritization and failover.
 */
public class AsyncConnectionManager implements AutoCloseable {
<span class="fc" id="L25">    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final Executor executor;
    private final AsyncFailbackManager failbackManager;

    /**
     * Create a new AsyncConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public AsyncConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="fc" id="L41">        this(config, new AsyncConnectionPool(config, authenticationManager));</span>
<span class="fc" id="L42">    }</span>

    /**
     * Create a new AsyncConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="fc" id="L50">    AsyncConnectionManager(RustyClusterClientConfig config, AsyncConnectionPool connectionPool) {</span>
<span class="fc" id="L51">        this.config = config;</span>
<span class="fc" id="L52">        this.connectionPool = connectionPool;</span>
<span class="fc" id="L53">        this.executor = ForkJoinPool.commonPool(); // Use common pool for async operations</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="fc" id="L56">        this.sortedNodes = config.getNodes().stream()</span>
<span class="fc" id="L57">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="fc" id="L58">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="fc" id="L61">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

        // For async operations, we'll use a synchronous FailbackManager with the async pool
        // The health checks will be performed synchronously but the failback manager itself
        // runs in a separate thread, so it won't block async operations
<span class="fc" id="L66">        this.failbackManager = new AsyncFailbackManager(config, connectionPool, sortedNodes, currentNode);</span>
<span class="fc" id="L67">        this.failbackManager.start();</span>

<span class="fc" id="L69">        logger.info(&quot;AsyncConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="fc" id="L70">    }</span>

    /**
     * Execute an operation asynchronously with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation) {
<span class="fc" id="L80">        return executeWithFailoverAsync(operation, OperationType.READ, 0);</span>
    }

    /**
     * Execute an operation asynchronously with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param &lt;T&gt;           The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType) {
<span class="nc" id="L92">        return executeWithFailoverAsync(operation, operationType, 0);</span>
    }

    private &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType, int retryCount) {
<span class="fc bfc" id="L96" title="All 2 branches covered.">        if (retryCount &gt; config.getMaxRetries()) {</span>
<span class="fc" id="L97">            return CompletableFuture.failedFuture(</span>
                new NoAvailableNodesException(&quot;Operation failed after &quot; + retryCount + &quot; retries&quot;));
        }

        // Determine timeout based on operation type
<span class="pc bpc" id="L102" title="3 of 4 branches missed.">        long timeoutMs = switch (operationType) {</span>
<span class="fc" id="L103">            case READ -&gt; config.getReadTimeoutMs();</span>
<span class="nc" id="L104">            case WRITE -&gt; config.getWriteTimeoutMs();</span>
<span class="pc" id="L105">            case AUTH -&gt; config.getConnectionTimeoutMs();</span>
        };

<span class="fc" id="L108">        NodeConfig node = currentNode.get();</span>

        // For async operations, we'll handle authentication differently
        // We'll check authentication state and clear it if needed, but won't pre-authenticate
        // Authentication will happen automatically when the operation fails with auth error

<span class="fc" id="L114">        return connectionPool.borrowStubAsync(node)</span>
<span class="fc" id="L115">            .thenCompose(stub -&gt; {</span>
                try {
                    // Apply deadline per operation to avoid expired deadline issues
<span class="fc" id="L118">                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =</span>
<span class="fc" id="L119">                        stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
<span class="fc" id="L120">                    ListenableFuture&lt;T&gt; listenableFuture = operation.execute(stubWithDeadline);</span>
<span class="fc" id="L121">                    return toCompletableFuture(listenableFuture)</span>
<span class="fc" id="L122">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="fc" id="L123">                            connectionPool.returnStub(node, stub);</span>
<span class="fc" id="L124">                        });</span>
<span class="nc" id="L125">                } catch (Exception e) {</span>
<span class="nc" id="L126">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L127">                    return CompletableFuture.failedFuture(e);</span>
                }
            })
<span class="fc" id="L130">            .exceptionally(throwable -&gt; {</span>
<span class="fc" id="L131">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, throwable.getMessage());</span>

                // Check if this is an authentication error and try to re-authenticate
<span class="pc bpc" id="L134" title="3 of 6 branches missed.">                if (isAuthenticationError(throwable) &amp;&amp; operationType != OperationType.AUTH &amp;&amp; config.hasAuthentication()) {</span>
<span class="nc" id="L135">                    logger.info(&quot;Authentication error detected, clearing auth state and will retry&quot;);</span>
<span class="nc" id="L136">                    connectionPool.getAuthenticationManager().clearAuthentication();</span>
                }

                // Try to find the next available node
<span class="fc" id="L140">                var nextNode = findNextAvailableNode(node);</span>
<span class="pc bpc" id="L141" title="1 of 2 branches missed.">                if (nextNode != null) {</span>
<span class="fc" id="L142">                    currentNode.set(nextNode);</span>
<span class="fc" id="L143">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>

                    // Clear authentication state when switching nodes
                    // This will force re-authentication on the next operation
<span class="pc bpc" id="L147" title="1 of 2 branches missed.">                    if (config.hasAuthentication()) {</span>
<span class="nc" id="L148">                        connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="nc" id="L149">                        logger.debug(&quot;Cleared authentication state for node switch to: {}&quot;, nextNode);</span>
                    }
                }

<span class="fc" id="L153">                throw new RuntimeException(throwable);</span>
            })
<span class="fc" id="L155">            .handle((result, throwable) -&gt; {</span>
<span class="pc bpc" id="L156" title="1 of 2 branches missed.">                if (throwable != null) {</span>
                    // Retry with delay
<span class="fc" id="L158">                    CompletableFuture&lt;Void&gt; delay = new CompletableFuture&lt;&gt;();</span>
<span class="fc" id="L159">                    CompletableFuture.delayedExecutor(config.getRetryDelayMs(),</span>
                            java.util.concurrent.TimeUnit.MILLISECONDS, executor)
<span class="fc" id="L161">                        .execute(() -&gt; delay.complete(null));</span>
<span class="fc" id="L162">                    return delay.thenCompose(v -&gt; executeWithFailoverAsync(operation, operationType, retryCount + 1));</span>
                }
<span class="nc" id="L164">                return CompletableFuture.completedFuture(result);</span>
            })
<span class="pc bpc" id="L166" title="1 of 2 branches missed.">            .thenCompose(future -&gt; future instanceof CompletableFuture ?</span>
<span class="pc" id="L167">                (CompletableFuture&lt;T&gt;) future : CompletableFuture.completedFuture((T) future));</span>
    }

    /**
     * Convert ListenableFuture to CompletableFuture.
     */
    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(ListenableFuture&lt;T&gt; listenableFuture) {
<span class="fc" id="L174">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>

<span class="fc" id="L176">        listenableFuture.addListener(() -&gt; {</span>
            try {
<span class="nc" id="L178">                completableFuture.complete(listenableFuture.get());</span>
<span class="fc" id="L179">            } catch (Exception e) {</span>
<span class="fc" id="L180">                completableFuture.completeExceptionally(e);</span>
<span class="nc" id="L181">            }</span>
<span class="fc" id="L182">        }, executor);</span>

<span class="fc" id="L184">        return completableFuture;</span>
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="fc" id="L195">        var samePriorityNode = sortedNodes.stream()</span>
<span class="pc bpc" id="L196" title="1 of 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="fc" id="L197">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L198">                .findFirst();</span>

<span class="pc bpc" id="L200" title="1 of 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L201">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="fc" id="L205">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="fc bfc" id="L206" title="All 2 branches covered.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="fc" id="L207">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L208">                .findFirst();</span>

<span class="fc bfc" id="L210" title="All 2 branches covered.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="fc" id="L211">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="fc" id="L215">        return sortedNodes.stream()</span>
<span class="pc bpc" id="L216" title="1 of 2 branches missed.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="fc" id="L217">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L218">                .findFirst()</span>
<span class="fc" id="L219">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
        try {
            // For async operations, we'll do a simple check
            // In a real implementation, you might want to perform an async health check
<span class="fc" id="L232">            return connectionPool.borrowStubAsync(node)</span>
<span class="fc" id="L233">                .thenApply(stub -&gt; {</span>
<span class="fc" id="L234">                    connectionPool.returnStub(node, stub);</span>
<span class="fc" id="L235">                    return true;</span>
                })
<span class="fc" id="L237">                .exceptionally(e -&gt; {</span>
<span class="nc" id="L238">                    logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L239">                    return false;</span>
                })
<span class="fc" id="L241">                .join(); // This is not ideal for async, but needed for this interface</span>
<span class="nc" id="L242">        } catch (Exception e) {</span>
<span class="nc" id="L243">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L244">            return false;</span>
        }
    }

    /**
     * Check if an exception indicates an authentication error.
     *
     * @param throwable The throwable to check
     * @return True if this is an authentication error, false otherwise
     */
    private boolean isAuthenticationError(Throwable throwable) {
<span class="pc bpc" id="L255" title="1 of 2 branches missed.">        if (throwable == null) {</span>
<span class="nc" id="L256">            return false;</span>
        }

<span class="fc" id="L259">        String message = throwable.getMessage();</span>
<span class="pc bpc" id="L260" title="1 of 2 branches missed.">        if (message == null) {</span>
<span class="nc" id="L261">            return false;</span>
        }

        // Check for common authentication error patterns
<span class="fc" id="L265">        String lowerMessage = message.toLowerCase();</span>
<span class="pc bpc" id="L266" title="1 of 2 branches missed.">        return lowerMessage.contains(&quot;unauthenticated&quot;) ||</span>
<span class="nc bnc" id="L267" title="All 2 branches missed.">               lowerMessage.contains(&quot;authentication failed&quot;) ||</span>
<span class="nc bnc" id="L268" title="All 2 branches missed.">               lowerMessage.contains(&quot;invalid token&quot;) ||</span>
<span class="nc bnc" id="L269" title="All 2 branches missed.">               lowerMessage.contains(&quot;unauthorized&quot;) ||</span>
<span class="nc bnc" id="L270" title="All 4 branches missed.">               lowerMessage.contains(&quot;permission denied&quot;) ||</span>
               (throwable instanceof io.grpc.StatusRuntimeException &amp;&amp;
<span class="pc bnc" id="L272" title="All 2 branches missed.">                ((io.grpc.StatusRuntimeException) throwable).getStatus().getCode() == io.grpc.Status.Code.UNAUTHENTICATED);</span>
    }

    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L280">        failbackManager.close();</span>
<span class="fc" id="L281">        connectionPool.close();</span>
<span class="fc" id="L282">        logger.info(&quot;AsyncConnectionManager closed&quot;);</span>
<span class="fc" id="L283">    }</span>

    /**
     * Functional interface for async client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface AsyncClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return ListenableFuture with the result of the operation
         * @throws Exception If the operation fails
         */
        ListenableFuture&lt;T&gt; execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>