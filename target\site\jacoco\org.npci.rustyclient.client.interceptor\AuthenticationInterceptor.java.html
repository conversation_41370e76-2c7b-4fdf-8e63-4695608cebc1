<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuthenticationInterceptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.interceptor</a> &gt; <span class="el_source">AuthenticationInterceptor.java</span></div><h1>AuthenticationInterceptor.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.interceptor;

import io.grpc.*;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * gRPC client interceptor that automatically adds authentication headers to requests.
 */
public class AuthenticationInterceptor implements ClientInterceptor {
    
<span class="fc" id="L14">    private static final Logger logger = LoggerFactory.getLogger(AuthenticationInterceptor.class);</span>
<span class="fc" id="L15">    private static final Metadata.Key&lt;String&gt; AUTHORIZATION_HEADER = </span>
<span class="fc" id="L16">            Metadata.Key.of(&quot;authorization&quot;, Metadata.ASCII_STRING_MARSHALLER);</span>
    
    private final AuthenticationManager authenticationManager;
    
<span class="fc" id="L20">    public AuthenticationInterceptor(AuthenticationManager authenticationManager) {</span>
<span class="fc" id="L21">        this.authenticationManager = authenticationManager;</span>
<span class="fc" id="L22">    }</span>
    
    @Override
    public &lt;ReqT, RespT&gt; ClientCall&lt;ReqT, RespT&gt; interceptCall(
            MethodDescriptor&lt;ReqT, RespT&gt; method,
            CallOptions callOptions,
            Channel next) {
        
<span class="fc" id="L30">        return new ForwardingClientCall.SimpleForwardingClientCall&lt;ReqT, RespT&gt;(</span>
<span class="fc" id="L31">                next.newCall(method, callOptions)) {</span>
            
            @Override
            public void start(Listener&lt;RespT&gt; responseListener, Metadata headers) {
                // Add authorization header if authenticated
<span class="pc bpc" id="L36" title="1 of 2 branches missed.">                if (authenticationManager.isAuthenticated()) {</span>
<span class="nc" id="L37">                    String sessionToken = authenticationManager.getSessionToken();</span>
<span class="nc bnc" id="L38" title="All 2 branches missed.">                    if (sessionToken != null) {</span>
<span class="nc" id="L39">                        headers.put(AUTHORIZATION_HEADER, &quot;Bearer &quot; + sessionToken);</span>
<span class="nc" id="L40">                        logger.debug(&quot;Added authorization header for method: {}&quot;, method.getFullMethodName());</span>
                    }
                }
                
<span class="fc" id="L44">                super.start(responseListener, headers);</span>
<span class="fc" id="L45">            }</span>
        };
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>