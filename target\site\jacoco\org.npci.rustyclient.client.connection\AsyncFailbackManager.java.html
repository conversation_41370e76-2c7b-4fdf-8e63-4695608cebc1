<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncFailbackManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">AsyncFailbackManager.java</span></div><h1>AsyncFailbackManager.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.grpc.KeyValueServiceGrpc;
import org.npci.rustyclient.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages automatic failback to higher-priority nodes for async operations.
 */
public class AsyncFailbackManager implements AutoCloseable {
<span class="fc" id="L21">    private static final Logger logger = LoggerFactory.getLogger(AsyncFailbackManager.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final ScheduledExecutorService scheduler;
<span class="fc" id="L28">    private volatile boolean running = false;</span>

    /**
     * Create a new AsyncFailbackManager.
     *
     * @param config         The client configuration
     * @param connectionPool The async connection pool
     * @param sortedNodes    The list of nodes sorted by priority
     * @param currentNode    The current active node reference
     */
    public AsyncFailbackManager(RustyClusterClientConfig config, 
                               AsyncConnectionPool connectionPool,
                               List&lt;NodeConfig&gt; sortedNodes,
<span class="fc" id="L41">                               AtomicReference&lt;NodeConfig&gt; currentNode) {</span>
<span class="fc" id="L42">        this.config = config;</span>
<span class="fc" id="L43">        this.connectionPool = connectionPool;</span>
<span class="fc" id="L44">        this.sortedNodes = sortedNodes;</span>
<span class="fc" id="L45">        this.currentNode = currentNode;</span>
<span class="fc" id="L46">        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -&gt; {</span>
<span class="fc" id="L47">            Thread t = new Thread(r, &quot;AsyncFailbackManager&quot;);</span>
<span class="fc" id="L48">            t.setDaemon(true);</span>
<span class="fc" id="L49">            return t;</span>
        });
<span class="fc" id="L51">    }</span>

    /**
     * Start the failback manager.
     */
    public void start() {
<span class="pc bpc" id="L57" title="1 of 2 branches missed.">        if (!config.isEnableFailback()) {</span>
<span class="nc" id="L58">            logger.debug(&quot;Failback is disabled, not starting AsyncFailbackManager&quot;);</span>
<span class="nc" id="L59">            return;</span>
        }

<span class="pc bpc" id="L62" title="1 of 2 branches missed.">        if (running) {</span>
<span class="nc" id="L63">            logger.warn(&quot;AsyncFailbackManager is already running&quot;);</span>
<span class="nc" id="L64">            return;</span>
        }

<span class="fc" id="L67">        running = true;</span>
<span class="fc" id="L68">        scheduler.scheduleWithFixedDelay(</span>
            this::checkForFailback,
<span class="fc" id="L70">            config.getFailbackCheckIntervalMs(),</span>
<span class="fc" id="L71">            config.getFailbackCheckIntervalMs(),</span>
            TimeUnit.MILLISECONDS
        );
        
<span class="fc" id="L75">        logger.info(&quot;AsyncFailbackManager started with check interval: {}ms&quot;, config.getFailbackCheckIntervalMs());</span>
<span class="fc" id="L76">    }</span>

    /**
     * Stop the failback manager.
     */
    public void stop() {
<span class="fc" id="L82">        running = false;</span>
<span class="fc" id="L83">        scheduler.shutdown();</span>
        try {
<span class="pc bpc" id="L85" title="1 of 2 branches missed.">            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {</span>
<span class="nc" id="L86">                scheduler.shutdownNow();</span>
            }
<span class="nc" id="L88">        } catch (InterruptedException e) {</span>
<span class="nc" id="L89">            scheduler.shutdownNow();</span>
<span class="nc" id="L90">            Thread.currentThread().interrupt();</span>
<span class="fc" id="L91">        }</span>
<span class="fc" id="L92">        logger.info(&quot;AsyncFailbackManager stopped&quot;);</span>
<span class="fc" id="L93">    }</span>

    /**
     * Check if we can failback to a higher-priority node.
     */
    private void checkForFailback() {
<span class="pc bpc" id="L99" title="1 of 2 branches missed.">        if (!running) {</span>
<span class="nc" id="L100">            return;</span>
        }

        try {
<span class="fc" id="L104">            NodeConfig current = currentNode.get();</span>
<span class="pc bpc" id="L105" title="1 of 2 branches missed.">            if (current == null) {</span>
<span class="nc" id="L106">                return;</span>
            }

            // Find the highest priority node that's available
<span class="fc" id="L110">            findBestAvailableNodeAsync()</span>
<span class="fc" id="L111">                .thenAccept(bestAvailableNode -&gt; {</span>
<span class="pc bpc" id="L112" title="1 of 2 branches missed.">                    if (bestAvailableNode != null &amp;&amp; </span>
<span class="fc bfc" id="L113" title="All 2 branches covered.">                        bestAvailableNode.role().getPriority() &lt; current.role().getPriority()) {</span>
                        
<span class="fc" id="L115">                        logger.info(&quot;Failing back from {} (priority {}) to {} (priority {})&quot;,</span>
<span class="fc" id="L116">                            current, current.role().getPriority(),</span>
<span class="fc" id="L117">                            bestAvailableNode, bestAvailableNode.role().getPriority());</span>

<span class="fc" id="L119">                        currentNode.set(bestAvailableNode);</span>

                        // Clear authentication state when switching nodes during failback
                        // This will force re-authentication on the next operation
<span class="fc bfc" id="L123" title="All 2 branches covered.">                        if (config.hasAuthentication()) {</span>
<span class="fc" id="L124">                            connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="fc" id="L125">                            logger.debug(&quot;Cleared authentication state for failback to: {}&quot;, bestAvailableNode);</span>
                        }
                    }
<span class="fc" id="L128">                })</span>
<span class="fc" id="L129">                .exceptionally(e -&gt; {</span>
<span class="fc" id="L130">                    logger.warn(&quot;Error during async failback check: {}&quot;, e.getMessage());</span>
<span class="fc" id="L131">                    return null;</span>
                });
<span class="nc" id="L133">        } catch (Exception e) {</span>
<span class="nc" id="L134">            logger.warn(&quot;Error during failback check: {}&quot;, e.getMessage());</span>
<span class="fc" id="L135">        }</span>
<span class="fc" id="L136">    }</span>

    /**
     * Find the best available node (highest priority that's healthy) asynchronously.
     *
     * @return CompletableFuture with the best available node, or null if none are available
     */
    private CompletableFuture&lt;NodeConfig&gt; findBestAvailableNodeAsync() {
<span class="fc" id="L144">        return checkNodesSequentially(0);</span>
    }

    /**
     * Check nodes sequentially starting from the given index.
     *
     * @param nodeIndex The index of the node to check
     * @return CompletableFuture with the first healthy node found, or null
     */
    private CompletableFuture&lt;NodeConfig&gt; checkNodesSequentially(int nodeIndex) {
<span class="pc bpc" id="L154" title="1 of 2 branches missed.">        if (nodeIndex &gt;= sortedNodes.size()) {</span>
<span class="nc" id="L155">            return CompletableFuture.completedFuture(null);</span>
        }

<span class="fc" id="L158">        NodeConfig node = sortedNodes.get(nodeIndex);</span>
<span class="fc" id="L159">        return isNodeHealthyAsync(node)</span>
<span class="fc" id="L160">            .thenCompose(isHealthy -&gt; {</span>
<span class="pc bpc" id="L161" title="1 of 2 branches missed.">                if (isHealthy) {</span>
<span class="fc" id="L162">                    return CompletableFuture.completedFuture(node);</span>
                } else {
<span class="nc" id="L164">                    return checkNodesSequentially(nodeIndex + 1);</span>
                }
            });
    }

    /**
     * Check if a node is healthy asynchronously.
     *
     * @param node The node to check
     * @return CompletableFuture with true if the node is healthy, false otherwise
     */
    private CompletableFuture&lt;Boolean&gt; isNodeHealthyAsync(NodeConfig node) {
<span class="fc" id="L176">        return performHealthCheckAsync(node, 0);</span>
    }

    /**
     * Perform health checks recursively with retries.
     *
     * @param node The node to check
     * @param attempt The current attempt number
     * @return CompletableFuture with true if all health checks pass, false otherwise
     */
    private CompletableFuture&lt;Boolean&gt; performHealthCheckAsync(NodeConfig node, int attempt) {
<span class="pc bpc" id="L187" title="1 of 2 branches missed.">        if (attempt &gt;= config.getFailbackHealthCheckRetries()) {</span>
<span class="nc" id="L188">            return CompletableFuture.completedFuture(true);</span>
        }

<span class="fc" id="L191">        return connectionPool.borrowStubAsync(node)</span>
<span class="fc" id="L192">            .thenCompose(stub -&gt; {</span>
                try {
                    // Apply a short timeout for health checks
<span class="fc" id="L195">                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =</span>
<span class="fc" id="L196">                        stub.withDeadlineAfter(1000, TimeUnit.MILLISECONDS);</span>

                    // For async health checks with authentication, we'll use a simpler approach:
                    // Just check if we can establish a connection. The actual authentication
                    // will be handled when the node is actually used for operations.
<span class="fc bfc" id="L201" title="All 2 branches covered.">                    if (config.hasAuthentication()) {</span>
                        // For authenticated servers, just being able to get a stub is sufficient
                        // for health checking. The actual authentication will happen during failback.
<span class="fc" id="L204">                        logger.debug(&quot;Health check for authenticated node {} (attempt {}): Connection established&quot;,</span>
<span class="fc" id="L205">                            node, attempt + 1);</span>
<span class="fc" id="L206">                        connectionPool.returnStub(node, stub);</span>
<span class="fc" id="L207">                        return CompletableFuture.completedFuture(true);</span>
                    }

                    // Perform a simple ping operation (get a non-existent key)
<span class="fc" id="L211">                    RustyClusterProto.GetRequest healthCheckRequest = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="fc" id="L212">                        .setKey(&quot;__health_check__&quot;)</span>
<span class="fc" id="L213">                        .build();</span>

<span class="fc" id="L215">                    return toCompletableFuture(stubWithDeadline.get(healthCheckRequest))</span>
<span class="fc" id="L216">                        .thenApply(response -&gt; true)</span>
<span class="fc" id="L217">                        .exceptionally(e -&gt; {</span>
<span class="nc" id="L218">                            logger.debug(&quot;Health check failed for node {} (attempt {}): {}&quot;,</span>
<span class="nc" id="L219">                                node, attempt + 1, e.getMessage());</span>
<span class="nc" id="L220">                            return false;</span>
                        })
<span class="fc" id="L222">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="fc" id="L223">                            connectionPool.returnStub(node, stub);</span>
<span class="fc" id="L224">                        });</span>
<span class="nc" id="L225">                } catch (Exception e) {</span>
<span class="nc" id="L226">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L227">                    return CompletableFuture.completedFuture(false);</span>
                }
            })
<span class="fc" id="L230">            .thenCompose(success -&gt; {</span>
<span class="pc bpc" id="L231" title="2 of 4 branches missed.">                if (success &amp;&amp; attempt &lt; config.getFailbackHealthCheckRetries() - 1) {</span>
                    // Add a small delay between checks
<span class="nc" id="L233">                    CompletableFuture&lt;Void&gt; delay = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L234">                    CompletableFuture.delayedExecutor(100, TimeUnit.MILLISECONDS)</span>
<span class="nc" id="L235">                        .execute(() -&gt; delay.complete(null));</span>
<span class="nc" id="L236">                    return delay.thenCompose(v -&gt; performHealthCheckAsync(node, attempt + 1));</span>
                } else {
<span class="fc" id="L238">                    return CompletableFuture.completedFuture(success);</span>
                }
            })
<span class="fc" id="L241">            .exceptionally(e -&gt; {</span>
<span class="fc" id="L242">                logger.debug(&quot;Health check failed for node {} (attempt {}): {}&quot;, </span>
<span class="fc" id="L243">                    node, attempt + 1, e.getMessage());</span>
<span class="fc" id="L244">                return false;</span>
            });
    }

    /**
     * Convert a ListenableFuture to CompletableFuture.
     */
    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(com.google.common.util.concurrent.ListenableFuture&lt;T&gt; listenableFuture) {
<span class="fc" id="L252">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>
<span class="fc" id="L253">        com.google.common.util.concurrent.Futures.addCallback(</span>
            listenableFuture,
<span class="fc" id="L255">            new com.google.common.util.concurrent.FutureCallback&lt;T&gt;() {</span>
                @Override
                public void onSuccess(T result) {
<span class="fc" id="L258">                    completableFuture.complete(result);</span>
<span class="fc" id="L259">                }</span>

                @Override
                public void onFailure(Throwable t) {
<span class="nc" id="L263">                    completableFuture.completeExceptionally(t);</span>
<span class="nc" id="L264">                }</span>
            },
            Runnable::run
        );
<span class="fc" id="L268">        return completableFuture;</span>
    }

    @Override
    public void close() {
<span class="fc" id="L273">        stop();</span>
<span class="fc" id="L274">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>