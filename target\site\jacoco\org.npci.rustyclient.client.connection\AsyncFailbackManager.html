<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncFailbackManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_class">AsyncFailbackManager</span></div><h1>AsyncFailbackManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">103 of 440</td><td class="ctr2">76%</td><td class="bar">11 of 28</td><td class="ctr2">60%</td><td class="ctr1">14</td><td class="ctr2">37</td><td class="ctr1">25</td><td class="ctr2">110</td><td class="ctr1">3</td><td class="ctr2">23</td></tr></tfoot><tbody><tr><td id="a13"><a href="AsyncFailbackManager.java.html#L218" class="el_method">lambda$performHealthCheckAsync$5(NodeConfig, int, Throwable)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="24" alt="24"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a10"><a href="AsyncFailbackManager.java.html#L231" class="el_method">lambda$performHealthCheckAsync$10(int, NodeConfig, Boolean)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="13" alt="13"/></td><td class="ctr2" id="c19">43%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a21"><a href="AsyncFailbackManager.java.html#L82" class="el_method">stop()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="17" alt="17"/></td><td class="ctr2" id="c17">60%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a15"><a href="AsyncFailbackManager.java.html#L195" class="el_method">lambda$performHealthCheckAsync$7(NodeConfig, int, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="99" height="10" title="49" alt="49"/></td><td class="ctr2" id="c14">83%</td><td class="bar" id="d8"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i0">17</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a19"><a href="AsyncFailbackManager.java.html#L57" class="el_method">start()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="63" height="10" title="31" alt="31"/></td><td class="ctr2" id="c15">79%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="AsyncFailbackManager.java.html#L99" class="el_method">checkForFailback()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="21" alt="21"/></td><td class="ctr2" id="c16">72%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h3">4</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a17"><a href="AsyncFailbackManager.java.html#L236" class="el_method">lambda$performHealthCheckAsync$9(NodeConfig, int, Void)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="AsyncFailbackManager.java.html#L161" class="el_method">lambda$checkNodesSequentially$3(NodeConfig, int, Boolean)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">50%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="1" alt="1"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a16"><a href="AsyncFailbackManager.java.html#L235" class="el_method">lambda$performHealthCheckAsync$8(CompletableFuture)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a18"><a href="AsyncFailbackManager.java.html#L187" class="el_method">performHealthCheckAsync(NodeConfig, int)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="24" alt="24"/></td><td class="ctr2" id="c13">85%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="1" alt="1"/></td><td class="ctr2" id="e7">50%</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a2"><a href="AsyncFailbackManager.java.html#L154" class="el_method">checkNodesSequentially(int)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="20" alt="20"/></td><td class="ctr2" id="c12">86%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="1" alt="1"/></td><td class="ctr2" id="e8">50%</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a6"><a href="AsyncFailbackManager.java.html#L112" class="el_method">lambda$checkForFailback$1(NodeConfig, NodeConfig)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="107" height="10" title="53" alt="53"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="5" alt="5"/></td><td class="ctr2" id="e1">83%</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a11"><a href="AsyncFailbackManager.java.html#L242" class="el_method">lambda$performHealthCheckAsync$11(NodeConfig, int, Throwable)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="24" alt="24"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a0"><a href="AsyncFailbackManager.java.html#L28" class="el_method">AsyncFailbackManager(RustyClusterClientConfig, AsyncConnectionPool, List, AtomicReference)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="44" height="10" title="22" alt="22"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a22"><a href="AsyncFailbackManager.java.html#L252" class="el_method">toCompletableFuture(ListenableFuture)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="14" alt="14"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i12">3</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a9"><a href="AsyncFailbackManager.java.html#L47" class="el_method">lambda$new$0(Runnable)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="11" alt="11"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i13">3</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a7"><a href="AsyncFailbackManager.java.html#L130" class="el_method">lambda$checkForFailback$2(Throwable)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="7" alt="7"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a14"><a href="AsyncFailbackManager.java.html#L223" class="el_method">lambda$performHealthCheckAsync$6(NodeConfig, KeyValueServiceGrpc.KeyValueServiceFutureStub, Boolean, Throwable)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a5"><a href="AsyncFailbackManager.java.html#L176" class="el_method">isNodeHealthyAsync(NodeConfig)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a4"><a href="AsyncFailbackManager.java.html#L144" class="el_method">findBestAvailableNodeAsync()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a20"><a href="AsyncFailbackManager.java.html#L21" class="el_method">static {...}</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a3"><a href="AsyncFailbackManager.java.html#L273" class="el_method">close()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a12"><a href="AsyncFailbackManager.java.html#L216" class="el_method">lambda$performHealthCheckAsync$4(RustyClusterProto.GetResponse)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>